'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Mail,
  MessageCircle,
  Heart,
  Sparkles,
  ExternalLink,
  Copy,
  Check,
  Users,
} from 'lucide-react';
import { toast } from 'sonner';

export default function InfluencerSupportPage() {
  const [emailCopied, setEmailCopied] = useState(false);
  const supportEmail = '<EMAIL>';

  const copyEmailToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(supportEmail);
      setEmailCopied(true);
      toast.success('Email adresa kopirana!');
      setTimeout(() => setEmailCopied(false), 2000);
    } catch {
      toast.error('Greška pri kopiranju email adrese');
    }
  };

  const openEmailClient = () => {
    window.location.href = `mailto:${supportEmail}?subject=Influencer upit sa Influexus platforme`;
  };

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8 max-w-xl sm:max-w-2xl">
        <div className="bg-card/95 backdrop-blur-md border border-purple-500/20 shadow-xl sm:shadow-2xl shadow-purple-500/20 rounded-xl sm:rounded-2xl overflow-hidden relative">
          {/* Decorative background - reduced on mobile */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#7F5BFE]/5 via-[#F35BF6]/3 to-[#f04a13]/5 opacity-30 sm:opacity-50"></div>
          <div className="absolute top-0 right-0 w-16 h-16 sm:w-32 sm:h-32 bg-gradient-to-br from-purple-400/10 to-transparent rounded-full blur-xl sm:blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-12 h-12 sm:w-24 sm:h-24 bg-gradient-to-tr from-pink-400/10 to-transparent rounded-full blur-lg sm:blur-xl"></div>

          <div className="relative p-4 sm:p-8">
            {/* Header */}
            <div className="text-center space-y-3 sm:space-y-6 pb-4 sm:pb-8">
              {/* Icon */}
              <div className="flex justify-center">
                <div className="flex items-center justify-center w-14 h-14 sm:w-20 sm:h-20 rounded-full bg-gradient-to-br from-[#7F5BFE]/20 to-[#F35BF6]/20 border border-purple-500/20">
                  <Heart className="h-7 w-7 sm:h-10 sm:w-10 text-purple-600 dark:text-purple-400" />
                </div>
              </div>

              <div className="space-y-2 sm:space-y-4">
                <h1 className="text-xl sm:text-3xl font-bold text-foreground flex items-center justify-center gap-2 sm:gap-3 flex-wrap">
                  <Users className="h-5 w-5 sm:h-6 sm:w-6 text-purple-500" />
                  <span>Influencer Podrška</span>
                  <Sparkles className="h-5 w-5 sm:h-6 sm:w-6 text-purple-500" />
                </h1>

                <Badge className="bg-gradient-to-r from-[#7F5BFE] to-[#F35BF6] text-white border-0 text-xs sm:text-sm">
                  Podrška za influencere
                </Badge>
              </div>

              <p className="text-center text-muted-foreground leading-relaxed text-lg">
                Ovdje možete pronaći pomoć i podršku vezanu za vaš influencer
                profil i aktivnosti na platformi.
              </p>
            </div>

            {/* Content */}
            <div className="space-y-4 sm:space-y-8">
              {/* Info Card */}
              <div className="relative p-4 sm:p-6 rounded-lg sm:rounded-xl bg-gradient-to-r from-purple-500/5 via-pink-500/3 to-orange-500/5 border border-purple-500/20">
                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-lg bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex-shrink-0 mt-0.5 sm:mt-1">
                    <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div className="space-y-2 sm:space-y-3">
                    <h2 className="text-lg sm:text-xl font-semibold text-foreground">
                      Javite nam se za:
                    </h2>
                    <ul className="text-muted-foreground space-y-1.5 sm:space-y-2 text-sm sm:text-base">
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span>Pitanja o profilu i postavkama</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span>Pomoć s aplikacijama na kampanje</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span>Problemi s isplatama</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span>Problemi s komunikacijom s brendovima</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span>Općenita pitanja o platformi</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Email Section */}
              <div className="space-y-3 sm:space-y-4">
                <h2 className="text-lg sm:text-xl font-semibold text-foreground text-center">
                  Kontaktirajte nas
                </h2>

                <div className="flex items-center gap-2 sm:gap-3 p-3 sm:p-4 rounded-lg sm:rounded-xl bg-gradient-to-r from-accent/30 to-accent/20 border border-border/50">
                  <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 dark:text-purple-400 flex-shrink-0" />
                  <span className="font-mono text-sm sm:text-base text-foreground flex-1 break-all">
                    {supportEmail}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={copyEmailToClipboard}
                    className="h-8 w-8 sm:h-10 sm:w-10 p-0 hover:bg-purple-500/10 transition-all duration-300 group flex-shrink-0"
                  >
                    {emailCopied ? (
                      <Check className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-green-600 group-hover:scale-110 transition-transform duration-300" />
                    ) : (
                      <Copy className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-purple-600 dark:text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Action Button */}
              <div className="flex justify-center pt-2 sm:pt-4">
                <Button
                  onClick={openEmailClient}
                  size="default"
                  className="bg-gradient-to-r from-[#7F5BFE] via-[#F35BF6] to-[#f04a13] text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group px-6 py-2.5 sm:px-8 sm:py-3 text-sm sm:text-base"
                >
                  <Mail className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3 group-hover:scale-110 transition-transform duration-300" />
                  Pošaljite email
                  <ExternalLink className="h-3.5 w-3.5 sm:h-4 sm:w-4 ml-2 sm:ml-3 group-hover:scale-110 transition-transform duration-300" />
                </Button>
              </div>

              {/* Footer */}
              <div className="text-center pt-2 sm:pt-4">
                <p className="text-xs sm:text-sm text-muted-foreground/70">
                  Odgovorićemo vam u najkraćem mogućem roku
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
