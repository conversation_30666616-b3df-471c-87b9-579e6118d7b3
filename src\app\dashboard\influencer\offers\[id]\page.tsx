'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { AcceptButton } from '@/components/ui/accept-button';
import { RejectButton } from '@/components/ui/reject-button';
import {
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Euro,
  Building2,
  Send,
  FileText,
  User,
  MessageCircle,
} from 'lucide-react';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';
import { BackButton } from '@/components/ui/back-button';
import {
  getDirectOffer,
  updateOfferStatus,
  type DirectOfferWithDetails,
} from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import Link from 'next/link';
import { DirectOfferJobSubmissionForm } from '@/components/job-completion/DirectOfferJobSubmissionForm';
import { getJobCompletionByDirectOffer } from '@/lib/job-completions';
import { formatDate } from '@/lib/date-utils';
import { getOfferPaymentInfo } from '@/lib/offers';

export default function InfluencerOfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [offer, setOffer] = useState<DirectOfferWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [response, setResponse] = useState('');
  const [jobCompletion, setJobCompletion] = useState<Record<
    string,
    unknown
  > | null>(null);
  const [showJobSubmission, setShowJobSubmission] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<Record<
    string,
    unknown
  > | null>(null);

  // Helper function to render submission notes
  const renderSubmissionNotes = (jobCompletion: Record<string, unknown>) => {
    try {
      const submissionData = JSON.parse(
        jobCompletion.submission_notes as string
      );
      return (
        <div className="space-y-4">
          {/* Post Links */}
          {submissionData.post_links &&
            submissionData.post_links.length > 0 && (
              <div>
                <h4 className="font-medium mb-3 text-gray-800">
                  Objavljeni postovi:
                </h4>
                <div className="space-y-2">
                  {submissionData.post_links.map(
                    (link: string, index: number) => (
                      <div
                        key={index}
                        className="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100"
                      >
                        <a
                          href={link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-2 hover:underline"
                        >
                          <svg
                            className="h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                            />
                          </svg>
                          {link}
                        </a>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}

          {/* Optional Message */}
          {submissionData.message && (
            <div>
              <h4 className="font-medium mb-2 text-gray-800">
                Dodatna poruka:
              </h4>
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border border-green-100">
                <p className="text-sm text-gray-700">
                  {submissionData.message}
                </p>
              </div>
            </div>
          )}
        </div>
      );
    } catch {
      // Fallback for old format
      return (
        <div>
          <h4 className="font-medium mb-2">Vaše napomene:</h4>
          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
            {jobCompletion.submission_notes as string}
          </p>
        </div>
      );
    }
  };

  const loadOffer = useCallback(
    async (offerId: string) => {
      setIsLoading(true);
      try {
        const { data, error } = await getDirectOffer(offerId);
        if (error) {
          console.error('Error loading offer:', error);
          router.push('/dashboard/influencer/offers');
        } else {
          setOffer(data);

          // Load payment info if offer is accepted
          if (data && data.status === 'accepted') {
            const paymentData = await getOfferPaymentInfo(data.id);
            setPaymentInfo(paymentData);
          }
        }
      } catch (error) {
        console.error('Error loading offer:', error);
        router.push('/dashboard/influencer/offers');
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const loadJobCompletion = useCallback(async () => {
    try {
      const { data: jobCompletion, error } =
        await getJobCompletionByDirectOffer(params.id as string);
      if (error) {
        console.error('Error loading job completion:', error);
        setJobCompletion(null);
      } else {
        setJobCompletion(jobCompletion);
      }
    } catch (error) {
      console.error('Error loading job completion:', error);
      setJobCompletion(null);
    }
  }, [params.id]);

  useEffect(() => {
    if (params.id) {
      loadOffer(params.id as string);
      loadJobCompletion();
    }
  }, [params.id, loadOffer, loadJobCompletion]);

  const handleOfferResponse = async (status: 'accepted' | 'rejected') => {
    if (!offer || !response.trim()) {
      toast.error('Molimo unesite odgovor prije prihvatanja/odbijanja ponude');
      return;
    }

    setIsUpdating(true);
    try {
      const { error } = await updateOfferStatus(offer.id, status, response);
      if (error) {
        toast.error('Greška pri ažuriranju ponude');
      } else {
        toast.success(
          status === 'accepted' ? 'Ponuda je prihvaćena!' : 'Ponuda je odbijena'
        );
        // Small delay to ensure chat permissions are processed
        await new Promise(resolve => setTimeout(resolve, 500));
        await loadOffer(offer.id);
      }
    } catch (error) {
      console.error('Error updating offer:', error);
      toast.error('Greška pri ažuriranju ponude');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbijeno';
      case 'completed':
        return 'Završeno';
      case 'cancelled':
        return 'Otkazano';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!offer) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Ponuda nije pronađena
          </h2>
          <Link href="/dashboard/influencer/offers">
            <BackButton>Nazad na ponude</BackButton>
          </Link>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <Link href="/dashboard/influencer/offers">
              <BackButton />
            </Link>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              {offer.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Ponuda primljena{' '}
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </p>
          </div>
          <Badge
            variant="outline"
            className={`${getStatusColor(offer.status)} font-medium`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(offer.status)}
              <span>{getStatusText(offer.status)}</span>
            </div>
          </Badge>
        </div>

        {/* Priority Status Cards - Always at Top */}
        <div className="mb-6">
          {/* Payment Pending Status - Always shows as full width when payment not completed */}
          {offer.status === 'accepted' && !paymentInfo && (
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-amber-50/80 via-yellow-50/60 to-amber-100/80 dark:from-amber-950/20 dark:via-yellow-950/10 dark:to-amber-900/30 border border-amber-200/50 dark:border-amber-800/30 backdrop-blur-sm shadow-lg mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 via-yellow-100/20 to-amber-200/40 dark:from-amber-900/10 dark:via-yellow-900/5 dark:to-amber-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Clock className="h-5 w-5 text-amber-600" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-amber-600 to-yellow-600 bg-clip-text text-transparent">
                    Čeka se plaćanje biznisa
                  </h3>
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-amber-100/50 dark:border-amber-800/30">
                  <p className="text-amber-700 dark:text-amber-300 mb-3">
                    🎉 Prihvatili ste ponudu! Sada čekamo da biznis izvrši
                    plaćanje pre početka komunikacije i rada na projektu.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-amber-600 dark:text-amber-400">
                        Budžet ponude:
                      </span>
                      <span className="font-semibold text-amber-900 dark:text-amber-100">
                        {offer.budget.toLocaleString()} €
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-amber-600 dark:text-amber-400">
                        Status plaćanja:
                      </span>
                      <span className="font-medium text-amber-800 dark:text-amber-200">
                        U toku...
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-center">
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    Bićete obavešteni čim biznis izvrši plaćanje.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Two-column layout for job completion and payment completed cards on desktop */}
          {offer.status === 'accepted' && paymentInfo && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Job Completion Section */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                    Završetak posla
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    Označite posao kao završen kada završite sve dogovorene
                    aktivnosti
                  </p>
                  <div>
                    {!jobCompletion && !showJobSubmission ? (
                      <div className="text-center py-6">
                        <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Spremni za submission?
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          Kada završite sve dogovorene aktivnosti, možete
                          označiti posao kao završen.
                        </p>
                        <button
                          onClick={() => setShowJobSubmission(true)}
                          className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30"
                        >
                          <Send className="w-4 h-4" />
                          Označi kao završeno
                        </button>
                      </div>
                    ) : showJobSubmission && !jobCompletion ? (
                      <DirectOfferJobSubmissionForm
                        directOfferId={params.id as string}
                        offerTitle={offer.title}
                        businessName={offer.businesses?.company_name}
                        businessAvatar={offer.businesses?.profiles?.avatar_url}
                        proposedRate={offer.budget}
                        onSuccess={() => {
                          setShowJobSubmission(false);
                          loadJobCompletion();
                        }}
                        onCancel={() => setShowJobSubmission(false)}
                      />
                    ) : jobCompletion ? (
                      <div className="space-y-4">
                        <div className="text-center py-6">
                          <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                          <h3 className="text-lg font-medium mb-2">
                            Završetak posla je poslan!
                          </h3>
                          <p className="text-muted-foreground mb-4">
                            {jobCompletion.status === 'submitted' &&
                              'Vaš rad je poslan biznisu na pregled. Čekamo njihovu potvrdu.'}
                            {jobCompletion.status === 'approved' &&
                              'Biznis je odobrio vaš rad. Čestitamo!'}
                            {jobCompletion.status === 'rejected' &&
                              'Biznis je odbacio vaš rad. Molimo kontaktirajte ih za više informacija.'}
                          </p>
                          <Badge
                            variant={
                              (jobCompletion as Record<string, unknown>)
                                ?.status === 'approved'
                                ? 'default'
                                : (jobCompletion as Record<string, unknown>)
                                      ?.status === 'rejected'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                            className="mb-4"
                          >
                            {(jobCompletion as Record<string, unknown>)
                              ?.status === 'approved'
                              ? 'Odobreno'
                              : (jobCompletion as Record<string, unknown>)
                                    ?.status === 'rejected'
                                ? 'Odbijeno'
                                : (jobCompletion as Record<string, unknown>)
                                      ?.status === 'submitted'
                                  ? 'Na čekanju pregleda'
                                  : 'Pending'}
                          </Badge>
                        </div>

                        {!!(jobCompletion as Record<string, unknown>)
                          ?.submitted_at && (
                          <div className="text-center">
                            <p className="text-sm text-muted-foreground">
                              Poslano{' '}
                              {formatDistanceToNow(
                                new Date(
                                  (jobCompletion as Record<string, unknown>)
                                    .submitted_at as string
                                ),
                                { addSuffix: true, locale: hr }
                              )}
                            </p>
                          </div>
                        )}

                        {(jobCompletion as Record<string, unknown>)
                          ?.submission_notes ? (
                          <div>
                            {renderSubmissionNotes(
                              jobCompletion as Record<string, unknown>
                            )}
                          </div>
                        ) : null}

                        {(jobCompletion as Record<string, unknown>)
                          ?.business_notes ? (
                          <div>
                            <h4 className="font-medium mb-2">
                              Napomene biznisa:
                            </h4>
                            <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                              {String(
                                (jobCompletion as Record<string, unknown>)
                                  .business_notes
                              )}
                            </p>
                          </div>
                        ) : null}
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>

              {/* Payment Completed Status */}
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-green-100/80 dark:from-green-950/20 dark:via-emerald-950/10 dark:to-green-900/30 border border-green-200/50 dark:border-green-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-green-100/30 via-emerald-100/20 to-green-200/40 dark:from-green-900/10 dark:via-emerald-900/5 dark:to-green-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      Plaćanje završeno - možete početi!
                    </h3>
                  </div>
                  <p className="text-green-600 dark:text-green-400 text-sm mb-4">
                    Odlične vesti! Biznis je izvršio plaćanje.
                  </p>

                  {/* Centered Payment Amount */}
                  <div className="text-center py-6">
                    <Euro className="h-12 w-12 text-green-600 mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2 text-green-800 dark:text-green-200">
                      Osigurana sredstva
                    </h3>
                    {paymentInfo && (
                      <div className="text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-4">
                        {(
                          paymentInfo as Record<string, unknown>
                        )?.payment_amount?.toLocaleString()}{' '}
                        €
                      </div>
                    )}
                    <p className="text-green-700 dark:text-green-300 text-sm">
                      Možete komunicirati i početi rad na projektu.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Offer Status - like Application Status */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Status ponude
                  </h3>
                  <Badge
                    variant="outline"
                    className={`${getStatusColor(offer.status)} font-medium`}
                  >
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(offer.status)}
                      <span>{getStatusText(offer.status)}</span>
                    </div>
                  </Badge>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Ponuda primljena:
                    </span>
                    <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                      {formatDistanceToNow(new Date(offer.created_at), {
                        addSuffix: true,
                        locale: hr,
                      })}
                    </span>
                  </div>
                  {offer.deadline && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-red-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Rok za završetak:
                      </span>
                      <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">
                        {formatDate(offer.deadline)}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Offer Details - like Campaign Details */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6 space-y-4">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Detalji ponude
                </h3>

                <div>
                  <h4 className="font-medium text-lg text-gray-900 dark:text-gray-100">
                    {offer.title}
                  </h4>
                  {((offer.offer_type === 'custom' && offer.description) ||
                    (offer.offer_type === 'package_order' &&
                      offer.description &&
                      !offer.description.startsWith('Narudžba paketa'))) && (
                    <p className="text-gray-700 dark:text-gray-300 mt-1 leading-relaxed">
                      {offer.description}
                    </p>
                  )}
                </div>

                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                  <div className="flex items-center gap-2">
                    <Euro className="w-4 h-4 text-purple-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Budžet ponude:
                    </span>
                    <span className="font-semibold text-gray-900 dark:text-gray-100">
                      {offer.budget?.toLocaleString()} €
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Platforms and Content Types */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  Platforme i tipovi sadržaja
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Gde influencer treba da objavi sadržaj i koji tip sadržaja
                </p>
                <div className="space-y-3">
                  {offer.platforms
                    .map((platform, platformIndex) => {
                      // Filter content types that belong to this platform
                      const platformContentTypes = offer.content_types.filter(
                        type => {
                          const lowerType = type.toLowerCase();
                          const lowerPlatform = platform.toLowerCase();

                          // First check: exact platform name match in content type
                          if (lowerType.includes(lowerPlatform)) {
                            return true;
                          }

                          // Second check: platform-specific content type mappings
                          // Only match if the content type doesn't already mention another platform
                          const mentionsOtherPlatform =
                            (lowerPlatform !== 'instagram' &&
                              lowerType.includes('instagram')) ||
                            (lowerPlatform !== 'tiktok' &&
                              lowerType.includes('tiktok')) ||
                            (lowerPlatform !== 'youtube' &&
                              lowerType.includes('youtube')) ||
                            (lowerPlatform !== 'facebook' &&
                              lowerType.includes('facebook')) ||
                            (lowerPlatform !== 'twitter' &&
                              lowerType.includes('twitter'));

                          if (mentionsOtherPlatform) {
                            return false;
                          }

                          // Platform-specific generic mappings (only if no other platform is mentioned)
                          if (lowerPlatform === 'instagram') {
                            return (
                              lowerType.includes('post') ||
                              lowerType.includes('story') ||
                              lowerType.includes('reel') ||
                              lowerType.includes('photo')
                            );
                          }
                          if (lowerPlatform === 'tiktok') {
                            return (
                              lowerType.includes('video') ||
                              lowerType.includes('short')
                            );
                          }
                          if (lowerPlatform === 'youtube') {
                            return (
                              lowerType.includes('video') ||
                              lowerType.includes('short')
                            );
                          }
                          if (lowerPlatform === 'facebook') {
                            return (
                              lowerType.includes('post') ||
                              lowerType.includes('video')
                            );
                          }
                          if (lowerPlatform === 'twitter') {
                            return (
                              lowerType.includes('tweet') ||
                              lowerType.includes('post')
                            );
                          }

                          return false;
                        }
                      );

                      // Only show platform if it has content types
                      if (platformContentTypes.length === 0) return null;

                      return (
                        <div
                          key={platformIndex}
                          className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30"
                        >
                          <div className="flex items-center gap-2 mb-2">
                            <PlatformIconSimple platform={platform} size="lg" />
                            <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">
                              {platform}
                            </span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {platformContentTypes.map((type, typeIndex) => (
                              <Badge
                                key={typeIndex}
                                variant="secondary"
                                className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                              >
                                {type === 'post'
                                  ? 'Photo Feed Post'
                                  : type === 'video'
                                    ? 'Video'
                                    : type === 'story'
                                      ? 'Story'
                                      : type === 'reel'
                                        ? 'Reel'
                                        : type === 'blog'
                                          ? 'Blog Post'
                                          : type}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      );
                    })
                    .filter(Boolean)}
                </div>
              </div>
            </div>

            {/* Requirements */}
            {offer.requirements &&
              (offer.offer_type === 'custom' ||
                (offer.offer_type === 'package_order' &&
                  !offer.requirements.startsWith(
                    'Automatski kreirana narudžba'
                  ))) && (
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <FileText className="h-5 w-5 text-purple-500" />
                      <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        Specifični zahtjevi
                      </h3>
                    </div>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {offer.requirements}
                    </p>
                  </div>
                </div>
              )}

            {/* Deliverables */}
            {(offer as any).deliverables && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Očekivani rezultati
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {(offer as any).deliverables}
                  </p>
                </div>
              </div>
            )}

            {/* Business Message */}
            {offer.business_message && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Building2 className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Poruka Brenda
                    </h3>
                  </div>
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {offer.business_message}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Influencer Response Display */}
            {offer.influencer_response && offer.status !== 'pending' && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Vaš odgovor
                    </h3>
                  </div>
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {offer.influencer_response}
                    </p>
                  </div>
                  <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                    {offer.status === 'accepted' && offer.accepted_at && (
                      <p>
                        Prihvaćeno{' '}
                        {formatDistanceToNow(new Date(offer.accepted_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && offer.rejected_at && (
                      <p>
                        Odbijeno{' '}
                        {formatDistanceToNow(new Date(offer.rejected_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Response Section */}
            {offer.status === 'pending' && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6 space-y-4">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Vaš odgovor
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Molimo odgovorite na ponudu i prihvatite ili odbijte je
                  </p>
                  <Textarea
                    placeholder="Unesite vaš odgovor na ponudu..."
                    value={response}
                    onChange={e => setResponse(e.target.value)}
                    rows={4}
                    className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                  />
                  <div className="flex gap-3">
                    <AcceptButton
                      onClick={() => handleOfferResponse('accepted')}
                      disabled={isUpdating || !response.trim()}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium"
                    >
                      <CheckCircle className="w-4 h-4" />
                      Prihvati ponudu
                    </AcceptButton>
                    <RejectButton
                      onClick={() => handleOfferResponse('rejected')}
                      disabled={isUpdating || !response.trim()}
                      className="flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium"
                    >
                      <XCircle className="w-4 h-4" />
                      Odbij ponudu
                    </RejectButton>
                  </div>
                </div>
              </div>
            )}

            {/* Response Status */}
            {offer.status !== 'pending' && offer.responded_at && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                    Status odgovora
                  </h3>
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    {offer.status === 'accepted' && (
                      <p>
                        Ponuda prihvaćena{' '}
                        {formatDistanceToNow(new Date(offer.responded_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && (
                      <p>
                        Ponuda odbijena{' '}
                        {formatDistanceToNow(new Date(offer.responded_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                  Biznis
                </h3>

                <div className="flex items-center gap-3">
                  <Avatar className="h-16 w-16 border-2 border-white/50">
                    <AvatarImage
                      src={offer.businesses?.profiles?.avatar_url || ''}
                      alt={offer.businesses?.company_name || 'Business'}
                    />
                    <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-lg">
                      <Building2 className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                      {offer.businesses?.company_name ||
                        offer.businesses?.profiles?.username ||
                        'Business'}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      @{offer.businesses?.profiles?.username || 'unknown'}
                    </p>
                  </div>
                </div>

                <Link
                  href={`/business/${offer.businesses?.profiles?.username || 'unknown'}`}
                >
                  <button className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                    <Building2 className="h-4 w-4" />
                    Pogledaj biznis profil
                  </button>
                </Link>
              </div>
            </div>

            {/* Chat Communication */}
            {offer.status === 'accepted' && paymentInfo && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                    Komunikacija
                  </h3>
                  <button
                    onClick={() => {
                      window.location.href = `/dashboard/chat?offer=${offer.id}`;
                    }}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  >
                    <MessageCircle className="h-4 w-4" />
                    Otvori chat
                  </button>
                </div>
              </div>
            )}

            {/* Timeline */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                  Istorija
                </h3>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Ponuda primljena
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {formatDistanceToNow(new Date(offer.created_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    </div>
                  </div>

                  {offer.status === 'accepted' && offer.responded_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Ponuda prihvaćena
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.responded_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}

                  {offer.status === 'rejected' && offer.responded_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Ponuda odbijena
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.responded_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
