'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import PlatformIconSimple from '@/components/ui/platform-icon-simple';

import { createCampaign } from '@/lib/campaigns';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { ContentType } from '@/lib/types';

// Schema za validaciju
const campaignSchema = z.object({
  // Korak 1: <PERSON><PERSON>, tipovi sad<PERSON>, bud<PERSON>et, naziv
  title: z
    .string()
    .min(5, 'Naziv mora imati najmanje 5 karaktera')
    .max(200, 'Naziv je predugačak'),
  budget: z
    .number()
    .min(25, 'Minimalni budžet je 25 €')
    .max(25000, 'Maksimalni budžet je 25,000 €'),

  // Korak 2: Opis kampanje
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),

  // Korak 3: Kategorije
  selectedCategories: z
    .array(z.number())
    .min(1, 'Odaberite najmanje jednu kategoriju')
    .max(3, 'Možete odabrati maksimalno 3 kategorije'),

  // Content types
  content_types: z.array(z.enum(['post', 'story', 'reel', 'video', 'blog'])).optional(),

  // Korak 4: Pol i dob influencera
  gender: z.enum(['male', 'female', 'all']).optional(),
  ageRangeMin: z.number().min(13).max(65).optional(),
  ageRangeMax: z.number().min(13).max(65).optional(),

  // Korak 5: Obavezne poruke i zabrane
  hashtags: z.string().optional(),
  doNotMention: z.string().optional(),

  // Korak 6: Dodatne napomene
  additionalNotes: z.string().optional(),

  // Ostali podaci
  collaborationType: z.enum(['paid', 'barter', 'hybrid']),
  showBusinessName: z.boolean(),
  isFeatured: z.boolean(),
});

type CampaignForm = z.infer<typeof campaignSchema>;

// Tipovi za platforme i sadržaj
interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface PlatformContentType {
  platform: string;
  types: string[];
}

// Mapiranje platformi na dostupne tipove sadržaja
const platformContentTypes: PlatformContentType[] = [
  {
    platform: 'Instagram',
    types: ['Photo post', 'Video', 'Story'],
  },
  {
    platform: 'TikTok',
    types: ['Video'],
  },
  {
    platform: 'YouTube',
    types: ['Video', 'Shorts'],
  },
];

// Mapiranje tipova sadržaja iz forme na enum vrednosti u bazi
const mapContentTypeToEnum = (contentType: string): ContentType => {
  const mapping: { [key: string]: ContentType } = {
    'Photo post': 'post',
    Video: 'video',
    Story: 'story',
    Shorts: 'reel', // YouTube Shorts mapiramo na reel
  };
  return mapping[contentType] || (contentType.toLowerCase() as ContentType);
};

interface CreateCampaignFormProps {
  initialData?: Partial<CampaignForm>;
  initialPlatforms?: Array<{
    platform_id: number;
    platforms: { name: string };
  }>;
  onSubmit?: (data: CampaignForm) => void;
  onSuccess?: (campaignId: string) => void;
  onCancel?: () => void;
  onSaveDraft?: (data: CampaignForm) => void;
  submitButtonText?: string;
  isLoading?: boolean;
  externalLoading?: boolean;
  isEditing?: boolean;
}

export function CreateCampaignForm({
  initialData,
  initialPlatforms,
  onSubmit,
  onSuccess,
  onCancel,
  onSaveDraft,
  submitButtonText,
  isLoading: externalLoading,
  externalLoading: externalLoadingProp,
  isEditing,
}: CreateCampaignFormProps) {
  const router = useRouter();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [currentStep, setCurrentStep] = useState(1);

  // State za Korak 1: Platforme i tipovi sadržaja
  const [selectedPlatformData, setSelectedPlatformData] = useState<{
    [platformId: number]: {
      selected: boolean;
      contentTypes: string[];
    };
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
    clearErrors,
    getValues,
  } = useForm<CampaignForm>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      collaborationType: 'paid',
      gender: 'all',
      showBusinessName: false,
      isFeatured: false,
      selectedCategories: [],
      ...initialData,
    },
  });

  // Load platforms and categories on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const [platformsResult, categoriesResult] = await Promise.all([
          supabase
            .from('platforms')
            .select('*')
            .eq('is_active', true)
            .not('name', 'in', '(Facebook,Twitter/X)')
            .order('name'),
          supabase.from('categories').select('*').order('name'),
        ]);

        if (platformsResult.data) {
          setPlatforms(platformsResult.data as Platform[]);
          // Initialize platform data state
          const initialPlatformData: Record<
            number,
            { selected: boolean; contentTypes: string[] }
          > = {};
          platformsResult.data.forEach(platform => {
            // Check if this platform was previously selected (for edit mode)
            const existingPlatform = initialPlatforms?.find(
              ip => ip.platform_id === platform.id
            );
            initialPlatformData[platform.id] = {
              selected: !!existingPlatform,
              contentTypes:
                existingPlatform && initialData?.content_types
                  ? mapDatabaseToUIContentTypes(initialData.content_types)
                  : [],
            };
          });
          setSelectedPlatformData(initialPlatformData);
        }
        if (categoriesResult.data) setCategories(categoriesResult.data as Category[]);
      } catch {
        toast.error('Greška pri učitavanju podataka');
      }
    };

    loadData();
  }, [initialPlatforms, initialData]);

  // Helper funkcije za rad sa platformama
  const togglePlatform = (platformId: number) => {
    setSelectedPlatformData(prev => ({
      ...prev,
      [platformId]: {
        ...prev[platformId],
        selected: !prev[platformId]?.selected,
        contentTypes: prev[platformId]?.selected
          ? []
          : prev[platformId]?.contentTypes || [],
      },
    }));
  };

  const toggleContentType = (platformId: number, contentType: string) => {
    setSelectedPlatformData(prev => {
      const currentTypes = prev[platformId]?.contentTypes || [];
      const newTypes = currentTypes.includes(contentType)
        ? currentTypes.filter(type => type !== contentType)
        : [...currentTypes, contentType];

      return {
        ...prev,
        [platformId]: {
          ...prev[platformId],
          contentTypes: newTypes,
        },
      };
    });
  };

  const getAvailableContentTypes = (platformName: string): string[] => {
    const platformConfig = platformContentTypes.find(
      p => p.platform === platformName
    );
    return platformConfig?.types || [];
  };

  // Map database content types to UI content types
  const mapDatabaseToUIContentTypes = (dbTypes: string[]): string[] => {
    const mapping: { [key: string]: string } = {
      post: 'Photo post',
      video: 'Video',
      story: 'Story',
      reel: 'Shorts',
    };
    return dbTypes.map(type => mapping[type] || type);
  };

  // Step validation functions
  const validateStep1 = () => {
    const values = getValues();
    const errors = [];

    // Proverava da li je odabrana najmanje jedna platforma sa tipom sadržaja
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([, data]) => data.selected && data.contentTypes.length > 0
    );

    if (selectedPlatforms.length === 0) {
      errors.push('Morate odabrati najmanje jednu platformu sa tipom sadržaja');
    }

    if (!values.budget || values.budget <= 0) {
      errors.push('Budžet mora biti veći od 0');
    }

    if (!values.title?.trim()) {
      errors.push('Naziv kampanje je obavezan');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    const values = getValues();
    const errors = [];

    if (!values.description?.trim()) {
      errors.push('Opis kampanje je obavezan');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const validateStep3 = () => {
    const selectedCategories = watch('selectedCategories') || [];
    const errors = [];

    if (selectedCategories.length === 0) {
      errors.push('Morate odabrati najmanje jednu kategoriju');
    }
    if (selectedCategories.length > 3) {
      errors.push('Možete odabrati maksimalno 3 kategorije');
    }

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const nextStep = () => {
    clearErrors('root');

    if (currentStep === 1 && !validateStep1()) return;
    if (currentStep === 2 && !validateStep2()) return;
    if (currentStep === 3 && !validateStep3()) return;

    setCurrentStep(prev => Math.min(prev + 1, 6)); // Sada imamo 6 koraka
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const onFormSubmit = async (data: CampaignForm) => {
    if (!user) {
      toast.error('Morate biti ulogovani da biste kreirali kampanju');
      return;
    }

    // If onSubmit prop is provided (edit mode), use it instead of creating new campaign
    if (onSubmit) {
      // Prepare platform data for edit mode
      const selectedPlatforms = Object.entries(selectedPlatformData)
        .filter(([, data]) => data.selected && data.contentTypes.length > 0)
        .map(([platformId]) => parseInt(platformId));

      // Collect all content types for the campaign and map to enum values
      const allContentTypes = Object.entries(selectedPlatformData)
        .filter(([, data]) => data.selected && data.contentTypes.length > 0)
        .flatMap(([, data]) => data.contentTypes)
        .map(type => mapContentTypeToEnum(type))
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      const enhancedData = {
        ...data,
        selectedPlatforms,
        content_types: allContentTypes as ContentType[],
      };

      onSubmit(enhancedData);
      return;
    }

    try {
      setIsLoading(true);

      // Prepare platform and content type data
      const selectedPlatforms = Object.entries(selectedPlatformData)
        .filter(([, data]) => data.selected && data.contentTypes.length > 0)
        .map(([platformId, data]) => ({
          platformId: parseInt(platformId),
          contentTypes: data.contentTypes,
        }));

      // Collect all content types for the campaign and map to enum values
      const allContentTypes = selectedPlatforms
        .flatMap(platform => platform.contentTypes)
        .map(type => mapContentTypeToEnum(type))
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      // Create campaign with all data
      const campaignData = {
        business_id: user.id,
        title: data.title,
        description: data.description,
        budget: data.budget,
        content_types: (allContentTypes.length > 0 ? allContentTypes : ['video']) as ContentType[], // Default to video if none selected
        collaboration_type: data.collaborationType,
        gender: data.gender === 'all' ? null : data.gender,
        age_range_min: data.ageRangeMin || null,
        age_range_max: data.ageRangeMax || null,
        hashtags: data.hashtags
          ? data.hashtags.split(',').map(h => h.trim())
          : null,
        do_not_mention: data.doNotMention
          ? data.doNotMention.split(',').map(d => d.trim())
          : null,
        additional_notes: data.additionalNotes || null,
        show_business_name: data.showBusinessName,
        is_featured: data.isFeatured,
        status: 'draft' as const,
      };

      const { data: campaign, error: campaignError } =
        await createCampaign(campaignData);

      if (campaignError) {
        console.error('Campaign creation error:', campaignError);
        toast.error('Greška pri kreiranju kampanje');
        return;
      }

      if (!campaign) {
        toast.error('Greška pri kreiranju kampanje');
        return;
      }

      // Save platform associations (skip if RLS blocks it)
      if (selectedPlatforms.length > 0) {
        try {
          const platformInserts = selectedPlatforms.map(platform => ({
            campaign_id: campaign.id,
            platform_id: platform.platformId,
            content_types: platform.contentTypes,
          }));

          const { error: platformError } = await supabase
            .from('campaign_platforms')
            .insert(platformInserts);

          if (platformError && platformError.code !== '42501') {
            console.error('Platform association error:', platformError);
          }
        } catch {
          console.log('Platform associations skipped due to RLS policy');
        }
      }

      // Save category associations (skip if RLS blocks it)
      if (data.selectedCategories && data.selectedCategories.length > 0) {
        try {
          const categoryInserts = data.selectedCategories.map(categoryId => ({
            campaign_id: campaign.id,
            category_id: categoryId,
          }));

          const { error: categoryError } = await supabase
            .from('campaign_categories')
            .insert(categoryInserts);

          if (categoryError && categoryError.code !== '42501') {
            console.error('Category association error:', categoryError);
          }
        } catch {
          console.log('Category associations skipped due to RLS policy');
        }
      }

      toast.success('Kampanja je uspešno kreirana!');

      // Handle success callbacks
      // if (onSubmit) {
      //   onSubmit(data as CampaignForm);
      // } else 
      if (onSuccess) {
        onSuccess(campaign.id);
      } else {
        router.push(`/campaigns/${campaign.id}`);
      }
    } catch {
      toast.error('Greška pri kreiranju kampanje');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit as any)} className="space-y-6">
      {/* Progress indicator */}
      <div className="mb-8 relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg p-6">
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
        <div className="relative">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-white">
              Korak {currentStep} od 6
            </span>
            <span className="text-sm text-white/70">
              {Math.round((currentStep / 6) * 100)}% završeno
            </span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-3">
            <div
              className="bg-white h-3 rounded-full transition-all duration-300 shadow-sm"
              style={{ width: `${(currentStep / 6) * 100}%` }}
            />
          </div>
        </div>
      </div>

      {/* Error display */}
      {errors.root && (
        <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4 mb-6">
          <p className="text-sm text-red-300">{errors.root.message}</p>
        </div>
      )}

      {/* Step 1: Platforme, tipovi sadržaja, budžet i naziv */}
      {currentStep === 1 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 1: Odaberite platforme i tipove sadržaja
              </h3>
              <p className="text-white/80">
                Odaberite šta želite da influencer odradi? Na kojoj platformi i
                kroz koji tip sadržaja da Vas promoviše?
              </p>
            </div>
            <div className="space-y-6">
              {/* Naziv kampanje */}
              <div>
                <Label htmlFor="title" className="text-white font-medium">
                  Naziv kampanje *
                </Label>
                <Input
                  id="title"
                  {...register('title')}
                  placeholder="npr. Promotivni video za naš restoran"
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
                {errors.title && (
                  <p className="text-sm text-red-300 mt-1">
                    {errors.title.message}
                  </p>
                )}
              </div>

              {/* Platforme i tipovi sadržaja */}
              <div>
                <div className="space-y-4">
                  {platforms.map(platform => {
                    const availableTypes = getAvailableContentTypes(
                      platform.name
                    );
                    const isSelected =
                      selectedPlatformData[platform.id]?.selected || false;
                    const selectedTypes =
                      selectedPlatformData[platform.id]?.contentTypes || [];

                    return (
                      <div
                        key={platform.id}
                        className="border border-white/30 rounded-lg p-4 bg-white/5"
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <Checkbox
                            id={`platform-${platform.id}`}
                            checked={isSelected}
                            onCheckedChange={() => togglePlatform(platform.id)}
                            className="data-[state=checked]:bg-white data-[state=checked]:border-white data-[state=checked]:text-purple-600"
                          />
                          <Label
                            htmlFor={`platform-${platform.id}`}
                            className="flex items-center gap-2 text-base font-medium cursor-pointer text-white"
                          >
                            <PlatformIconSimple
                              platform={platform.name}
                              size="lg"
                              variant="monochrome"
                              className="text-white"
                            />
                            {platform.name}
                          </Label>
                        </div>

                        {isSelected && (
                          <div className="ml-6 space-y-2">
                            <p className="text-sm text-white/70">
                              Tipovi sadržaja:
                            </p>
                            <div className="grid grid-cols-2 gap-2">
                              {availableTypes.map(type => (
                                <div
                                  key={type}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={`${platform.id}-${type}`}
                                    checked={selectedTypes.includes(type)}
                                    onCheckedChange={() =>
                                      toggleContentType(platform.id, type)
                                    }
                                    className="data-[state=checked]:bg-white data-[state=checked]:border-white data-[state=checked]:text-purple-600"
                                  />
                                  <Label
                                    htmlFor={`${platform.id}-${type}`}
                                    className="text-sm cursor-pointer text-white/80"
                                  >
                                    {type}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Maksimalni iznos */}
              <div>
                <Label htmlFor="budget" className="text-white font-medium">
                  Koliki je maksimalni iznos koji ste spremni platiti po
                  influenceru? *
                </Label>
                <p className="text-sm text-white/70 mb-2">
                  Ovaj iznos će biti prikazan influencerima kao maksimalna
                  cijena za kampanju. Influenceri se mogu prijaviti sa svojom
                  predloženom cijenom, a Vi birate koga ćete angažovati.
                </p>
                <div className="relative max-w-xs">
                  <Input
                    id="budget"
                    type="number"
                    {...register('budget', { valueAsNumber: true })}
                    placeholder="500"
                    className="pr-12 bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <span className="text-white/70 text-sm">€</span>
                  </div>
                </div>
                {errors.budget && (
                  <p className="text-sm text-red-300 mt-1">
                    {errors.budget.message}
                  </p>
                )}
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  onClick={nextStep}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Sledeći korak
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 2: Opis kampanje */}
      {currentStep === 2 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 2: Opis kampanje
              </h3>
              <p className="text-white/80">
                Opišite kampanju i šta očekujete od influencera
              </p>
            </div>
            <div className="space-y-4">
              <div>
                <Label htmlFor="description" className="text-white font-medium">
                  Opis kampanje *
                </Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Npr. Želimo da influencer posjeti naš restoran i snimi kratak video u kojem preporučuje naše jelo dana"
                  rows={6}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
                {errors.description && (
                  <p className="text-sm text-red-300 mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                >
                  Prethodni korak
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Sledeći korak
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Kategorije sadržaja */}
      {currentStep === 3 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 3: Kategorije sadržaja
              </h3>
              <p className="text-white/80">
                Postavite uslov - kakav sadržaj mora da kreira influencer koji
                se želi prijaviti na kampanju (min. 1, max. 3 kategorije)
              </p>
            </div>
            <div className="space-y-4">
              <div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.map(category => {
                    const selectedCategories =
                      watch('selectedCategories') || [];
                    const isSelected = selectedCategories.includes(category.id);
                    const canSelect =
                      selectedCategories.length < 3 || isSelected;

                    return (
                      <div
                        key={category.id}
                        className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                          isSelected
                            ? 'border-white bg-white/20 text-white'
                            : canSelect
                              ? 'border-white/30 hover:border-white/50 text-white/80 hover:text-white bg-white/5 hover:bg-white/10'
                              : 'border-white/20 opacity-50 cursor-not-allowed text-white/40'
                        }`}
                        onClick={() => {
                          if (!canSelect) return;

                          const currentSelected = selectedCategories;
                          const newSelected = isSelected
                            ? currentSelected.filter(id => id !== category.id)
                            : [...currentSelected, category.id];

                          setValue('selectedCategories', newSelected);
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{category.icon}</span>
                          <span className="text-sm font-medium">
                            {category.name}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
                {errors.selectedCategories && (
                  <p className="text-sm text-red-300 mt-2">
                    {errors.selectedCategories.message}
                  </p>
                )}
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                >
                  Prethodni korak
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Sledeći korak
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 4: Pol i dob influencera */}
      {currentStep === 4 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 4: Demografski uslovi
              </h3>
              <p className="text-white/80">
                Postavite uslove - kakve demografske karakteristike mora da ima
                influencer koji se želi prijaviti na kampanju
              </p>
            </div>
            <div className="space-y-6">
              {/* Pol influencera */}
              <div>
                <Label className="text-base font-medium text-white">
                  Pol influencera
                </Label>
                <div className="grid grid-cols-3 gap-3 mt-3">
                  {[
                    { value: 'male', label: 'Muški' },
                    { value: 'female', label: 'Ženski' },
                    { value: 'all', label: 'Svi' },
                  ].map(option => (
                    <div
                      key={option.value}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        watch('gender') === option.value
                          ? 'border-white bg-white/20 text-white'
                          : 'border-white/30 hover:border-white/50 text-white/80 hover:text-white bg-white/5 hover:bg-white/10'
                      }`}
                      onClick={() => setValue('gender', option.value as any)}
                    >
                      <div className="text-center">
                        <span className="font-medium">{option.label}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Dob influencera */}
              <div>
                <Label className="text-base font-medium text-white">
                  Dob influencera
                </Label>
                <div className="grid grid-cols-2 gap-4 mt-3">
                  <div>
                    <Label
                      htmlFor="ageRangeMin"
                      className="text-white font-medium"
                    >
                      Od godine
                    </Label>
                    <Input
                      id="ageRangeMin"
                      type="number"
                      {...register('ageRangeMin', { valueAsNumber: true })}
                      placeholder="18"
                      min="13"
                      max="65"
                      className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="ageRangeMax"
                      className="text-white font-medium"
                    >
                      Do godine
                    </Label>
                    <Input
                      id="ageRangeMax"
                      type="number"
                      {...register('ageRangeMax', { valueAsNumber: true })}
                      placeholder="35"
                      min="13"
                      max="65"
                      className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                >
                  Prethodni korak
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Sledeći korak
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 5: Obavezne poruke i zabrane */}
      {currentStep === 5 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 5: Obavezne poruke i zabrane
              </h3>
              <p className="text-white/80">
                Definirajte ključne fraze, hashtag-ove i zabrane za sadržaj
              </p>
            </div>
            <div className="space-y-6">
              {/* Obavezni hashtag-ovi */}
              <div>
                <Label htmlFor="hashtags" className="text-white font-medium">
                  Obavezni hashtag-ovi
                </Label>
                <p className="text-sm text-white/70 mb-2">
                  Unesite hashtag-ove koje influencer mora koristiti (odvojite
                  zarezom)
                </p>
                <Input
                  id="hashtags"
                  {...register('hashtags')}
                  placeholder="npr. #novoproizvod, #promocija, #sarajevo"
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
              </div>

              {/* Zabranjene stvari */}
              <div>
                <Label
                  htmlFor="doNotMention"
                  className="text-white font-medium"
                >
                  Zabranjene stvari
                </Label>
                <p className="text-sm text-white/70 mb-2">
                  Navedite šta ne smije biti u sadržaju (konkurentski brendovi,
                  određene riječi, itd.)
                </p>
                <Textarea
                  id="doNotMention"
                  {...register('doNotMention')}
                  placeholder="npr. konkurentski brendovi, jeftino, loš kvalitet"
                  rows={3}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                >
                  Prethodni korak
                </Button>
                <Button
                  type="button"
                  onClick={nextStep}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Sledeći korak
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 6: Dodatne napomene */}
      {currentStep === 6 && (
        <div className="relative overflow-hidden rounded-xl bg-white/10 border border-white/20 backdrop-blur-sm shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/5 pointer-events-none" />
          <div className="relative p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-2">
                Korak 6: Dodatne napomene
              </h3>
              <p className="text-white/80">
                Dodajte posebne smjernice, reference, ton komunikacije ili
                primjere dobrog sadržaja
              </p>
            </div>
            <div className="space-y-6">
              {/* Dodatne napomene */}
              <div>
                <Label
                  htmlFor="additionalNotes"
                  className="text-white font-medium"
                >
                  Dodatne napomene
                </Label>
                <Textarea
                  id="additionalNotes"
                  {...register('additionalNotes')}
                  placeholder="Slobodno polje gdje možete dati posebne smjernice, reference, ton komunikacije, primjere dobrog sadržaja. Nešto što bi dodatno pomoglo da influencer shvati šta se traži i kako da odradi zadatak."
                  rows={6}
                  className="bg-white/10 border-white/30 text-white placeholder:text-white/50 focus:border-white/50 focus:ring-white/20"
                />
              </div>

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  className="border-white/30 text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm"
                >
                  Prethodni korak
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || externalLoading || externalLoadingProp}
                  className="bg-white text-purple-600 hover:bg-white/90 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  {(isLoading || externalLoading || externalLoadingProp) && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin text-purple-600" />
                  )}
                  {submitButtonText || 'Kreiraj kampanju'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </form>
  );
}
